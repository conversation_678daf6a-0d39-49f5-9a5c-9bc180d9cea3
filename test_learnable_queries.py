#!/usr/bin/env python3
"""
Test script to verify the learnable queries implementation.
"""

import torch
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.visual_backbone import Qwen2_5_ContextVisionConfig, Qwen2_5_ContextVisionTransformerPretrainedModel

def test_learnable_queries():
    """Test the learnable queries functionality."""
    print("Testing learnable queries implementation...")
    
    # Create a test configuration
    config = Qwen2_5_ContextVisionConfig(
        depth=4,  # Smaller for testing
        hidden_size=256,  # Smaller for testing
        max_num_images=8,
        use_learnable_queries=True
    )
    
    # Create the model
    try:
        model = Qwen2_5_ContextVisionTransformerPretrainedModel(config)
        print("✓ Model created successfully")
    except Exception as e:
        print(f"✗ Failed to create model: {e}")
        return False
    
    # Test learnable queries parameter
    if hasattr(model, 'learnable_queries') and model.learnable_queries is not None:
        print(f"✓ Learnable queries parameter exists with shape: {model.learnable_queries.shape}")
        expected_shape = (config.max_num_images, config.hidden_size)
        if model.learnable_queries.shape == expected_shape:
            print(f"✓ Learnable queries have correct shape: {expected_shape}")
        else:
            print(f"✗ Learnable queries have wrong shape. Expected: {expected_shape}, Got: {model.learnable_queries.shape}")
            return False
    else:
        print("✗ Learnable queries parameter not found")
        return False
    
    # Test get_context_queries method
    try:
        # Create test grid_thw: [num_images, 3] where each row is [t, h, w]
        grid_thw = torch.tensor([
            [1, 4, 4],  # Image 1: 1*4*4 = 16 tokens
            [1, 2, 3],  # Image 2: 1*2*3 = 6 tokens
            [2, 2, 2],  # Image 3: 2*2*2 = 8 tokens
        ], dtype=torch.long)
        
        context_queries = model.get_context_queries(grid_thw)
        print(f"✓ get_context_queries method works, output shape: {context_queries.shape}")
        
        # Check if the total number of tokens is correct
        expected_total_tokens = (1*4*4) + (1*2*3) + (2*2*2)  # 16 + 6 + 8 = 30
        if context_queries.shape[0] == expected_total_tokens:
            print(f"✓ Total tokens correct: {expected_total_tokens}")
        else:
            print(f"✗ Total tokens incorrect. Expected: {expected_total_tokens}, Got: {context_queries.shape[0]}")
            return False
            
        # Check if the hidden dimension is correct
        if context_queries.shape[1] == config.hidden_size:
            print(f"✓ Hidden dimension correct: {config.hidden_size}")
        else:
            print(f"✗ Hidden dimension incorrect. Expected: {config.hidden_size}, Got: {context_queries.shape[1]}")
            return False
            
    except Exception as e:
        print(f"✗ get_context_queries method failed: {e}")
        return False
    
    # Test with too many images
    try:
        too_many_images = torch.ones(config.max_num_images + 1, 3, dtype=torch.long)
        model.get_context_queries(too_many_images)
        print("✗ Should have raised error for too many images")
        return False
    except ValueError as e:
        print(f"✓ Correctly raised error for too many images: {e}")
    except Exception as e:
        print(f"✗ Unexpected error for too many images: {e}")
        return False
    
    # Test with use_learnable_queries=False
    config_no_queries = Qwen2_5_ContextVisionConfig(
        depth=4,
        hidden_size=256,
        max_num_images=8,
        use_learnable_queries=False
    )
    
    try:
        model_no_queries = Qwen2_5_ContextVisionTransformerPretrainedModel(config_no_queries)
        if model_no_queries.learnable_queries is None:
            print("✓ Model without learnable queries created correctly")
        else:
            print("✗ Model should not have learnable queries when disabled")
            return False
            
        context_queries = model_no_queries.get_context_queries(grid_thw)
        if context_queries is None:
            print("✓ get_context_queries returns None when disabled")
        else:
            print("✗ get_context_queries should return None when disabled")
            return False
            
    except Exception as e:
        print(f"✗ Failed to test model without learnable queries: {e}")
        return False
    
    print("\n🎉 All tests passed! Learnable queries implementation is working correctly.")
    return True

if __name__ == "__main__":
    success = test_learnable_queries()
    sys.exit(0 if success else 1)
